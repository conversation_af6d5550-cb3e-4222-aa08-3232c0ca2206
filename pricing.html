<!doctype html>
<html lang="en">

<head>
   <link rel="stylesheet" href="./assets/libs/swiper/swiper-bundle.min.css" />
   <!-- Required meta tags -->
   <meta charset="utf-8" />
   <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
   <!-- Favicon icon-->
   <link rel="apple-touch-icon" sizes="180x180" href="./assets/images/favicon/apple-touch-icon.png" />
   <link rel="icon" type="image/png" sizes="32x32" href="./assets/images/favicon/favicon-32x32.png" />
   <link rel="icon" type="image/png" sizes="16x16" href="./assets/images/favicon/favicon-16x16.png" />
   <link rel="manifest" href="./assets/images/favicon/site.webmanifest" />
   <link rel="mask-icon" href="./assets/images/favicon/block-safari-pinned-tab.svg" color="#8b3dff" />
   <link rel="shortcut icon" href="./assets/images/favicon/favicon.ico" />
   <meta name="msapplication-TileColor" content="#8b3dff" />
   <meta name="msapplication-config" content="./assets/images/favicon/tile.xml" />

   <!-- Color modes -->
   <script src="./assets/js/vendors/color-modes.js"></script>

   <!-- Libs CSS -->
   <link href="./assets/libs/simplebar/dist/simplebar.min.css" rel="stylesheet" />
   <link href="./assets/libs/bootstrap-icons/font/bootstrap-icons.min.css" rel="stylesheet" />

   <!-- Scroll Cue -->
   <link rel="stylesheet" href="./assets/libs/scrollcue/scrollCue.css" />

   <!-- Box icons -->
   <link rel="stylesheet" href="./assets/fonts/css/boxicons.min.css" />

   <!-- Theme CSS -->
   <link rel="stylesheet" href="./assets/css/theme.min.css">

   <title>Aidapay - Data Plans Pricing | Compare Nigerian Network Data Bundles</title>

   <style>
      .network-selector {
         display: flex;
         justify-content: center;
         gap: 2rem;
         margin: 2rem 0;
         flex-wrap: wrap;
      }

      .network-circle {
         width: 120px;
         height: 120px;
         border-radius: 50%;
         display: flex;
         align-items: center;
         justify-content: center;
         cursor: pointer;
         transition: all 0.3s ease;
         border: 3px solid transparent;
         background: white;
         box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
         position: relative;
      }

      .network-circle:hover {
         transform: translateY(-5px);
         box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .network-circle.active {
         border-color: #8b3dff;
         transform: translateY(-5px);
         box-shadow: 0 8px 25px rgba(139, 61, 255, 0.3);
      }

      .network-circle.active::after {
         content: '✓';
         position: absolute;
         top: -10px;
         right: -10px;
         background: #8b3dff;
         color: white;
         width: 30px;
         height: 30px;
         border-radius: 50%;
         display: flex;
         align-items: center;
         justify-content: center;
         font-weight: bold;
         font-size: 16px;
      }

      .network-logo {
         width: 80px;
         height: 80px;
         object-fit: contain;
      }

      .network-name {
         position: absolute;
         bottom: -30px;
         left: 50%;
         transform: translateX(-50%);
         font-weight: 600;
         color: #333;
         font-size: 14px;
      }

      .pricing-table {
         display: none;
      }

      .pricing-table.active {
         display: block;
      }

      .plan-card {
         border: 1px solid #e9ecef;
         border-radius: 12px;
         padding: 1.5rem;
         margin-bottom: 1rem;
         transition: all 0.3s ease;
         background: white;
      }

      .plan-card:hover {
         border-color: #8b3dff;
         box-shadow: 0 4px 15px rgba(139, 61, 255, 0.1);
         transform: translateY(-2px);
      }

      .plan-price {
         font-size: 1.5rem;
         font-weight: 700;
         color: #8b3dff;
      }

      .plan-data {
         font-size: 1.25rem;
         font-weight: 600;
         color: #333;
      }

      .plan-validity {
         color: #6c757d;
         font-size: 0.9rem;
      }

      .plan-features {
         list-style: none;
         padding: 0;
         margin: 1rem 0;
      }

      .plan-features li {
         padding: 0.25rem 0;
         color: #6c757d;
         font-size: 0.9rem;
      }

      .plan-features li::before {
         content: '•';
         color: #8b3dff;
         margin-right: 0.5rem;
      }

      @media (max-width: 768px) {
         .network-circle {
            width: 100px;
            height: 100px;
         }

         .network-logo {
            width: 60px;
            height: 60px;
         }

         .network-selector {
            gap: 1rem;
         }
      }
   </style>
</head>

<body>
   <!-- Navbar -->
   <header>
      <div class="container">
         <nav class="navbar navbar-expand-lg navbar-boxed mx-auto mt-lg-3">
            <a class="navbar-brand" href="./index.html"><img src="./assets/images/logo/logo_aidapay.svg"
                  style="width: 80%;" alt /></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
               data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false"
               aria-label="Toggle navigation">
               <i class="bi bi-list"></i>
            </button>

            <div class="collapse navbar-collapse" id="navbarSupportedContent">
               <ul class="navbar-nav mx-auto align-items-lg-center">
                  <li class="nav-item dropdown">
                     <a class="nav-link" href="index.html" role="button" data-bs-toggle="dropdown" aria-expanded="false">Home</a>
                  </li>
                  <li class="nav-item dropdown">
                     <a class="nav-link" href="scan&pay.html" role="button" data-bs-toggle="dropdown" aria-expanded="false">Scan &
                        Pay</a>
                  </li>
                  <li class="nav-item dropdown">
                     <a class="nav-link active" href="pricing.html" role="button" data-bs-toggle="dropdown"
                        aria-expanded="false">Pricing</a>
                  </li>
                  <li class="nav-item dropdown">
                     <a class="nav-link" href="services.html" role="button" data-bs-toggle="dropdown"
                        aria-expanded="false">Services</a>
                  </li>
                  <li class="nav-item dropdown">
                     <a class="nav-link" href="contact.html" role="button" data-bs-toggle="dropdown"
                        aria-expanded="false">Contact</a>
                  </li>
                  <li class="nav-item dropdown">
                     <a class="nav-link" href="become-an-agent.html" role="button" data-bs-toggle="dropdown" aria-expanded="false">Become
                        An Agent</a>
                  </li>
               </ul>
               <div class="mt-3 mt-lg-0 d-flex align-items-center">
                  <a href="signin.html" class="btn btn-light mx-2">Login</a>
                  <a href="#" class="btn btn-primary">Create account</a>
               </div>
            </div>
         </nav>
      </div>
   </header>

   <main>
      <!--Hero section-->
      <section class="pt-lg-10" style="
               background: url(./assets/images/mobile-app/curvlines.svg), linear-gradient(111.42deg, #4a00b7 0.42%, #c13dff 81.76%, #c13dff 96.06%);
               background-position: center;
               background-repeat: no-repeat;
               background-size: cover;
            ">
         <div class="container">
            <div class="pt-5">
               <div class="row">
                  <div class="col-12">
                     <div class="row align-items-center gy-5 px-lg-6 position-relative">
                        <div class="col-md-8 mx-auto text-center" data-cue="slideInUp">
                           <div class="d-flex flex-column gap-7">
                              <div class="d-flex flex-column gap-3 mb-lg-10">
                                 <div class="d-flex flex-row align-items-center justify-content-center">
                                    <a href="#!"
                                       class="bg-opacity-50 text-bg-primary border border-primary badge px-3 py-2 fw-medium rounded-pill fs-6">
                                       <span class="">Compare All Nigerian Networks</span>
                                       <span class="ms-1">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                             fill="currentColor" class="bi bi-arrow-right" viewBox="0 0 16 16">
                                             <path fill-rule="evenodd"
                                                d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8">
                                             </path>
                                          </svg>
                                       </span>
                                    </a>
                                 </div>
                                 <div class="d-flex flex-column gap-5">
                                    <div class="d-flex flex-column gap-3">
                                       <h1 class="mb-0 text-white-stable display-4">Data Plans & Pricing</h1>
                                       <p class="mb-0 text-white-50 lead">Compare data plans from all major Nigerian networks. 
                                          Find the best deals and buy data instantly through Aidapay with our transparent ₦10 flat fee.
                                       <p>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </section>
      <!--Hero section end-->

      <!--Network Selection Section-->
      <section class="py-xl-9 py-6" data-cue="fadeIn">
         <div class="container">
            <div class="row">
               <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                  <div class="text-center mb-6">
                     <small class="text-primary text-uppercase ls-md fw-semibold">SELECT YOUR NETWORK</small>
                     <h2 class="my-3">Choose Your Preferred Network</h2>
                     <p class="mb-0 lead">Select any Nigerian network below to view their current data plan pricing. 
                        All plans can be purchased through Aidapay with just a ₦10 service fee.</p>
                  </div>
               </div>
            </div>
            
            <div class="network-selector">
               <div class="network-circle active" data-network="mtn">
                  <img src="./assets/images/networks/mtn-logo.png" alt="MTN" class="network-logo" 
                       onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                  <div style="display: none; font-weight: bold; color: #FFCB05; font-size: 24px;">MTN</div>
                  <span class="network-name">MTN</span>
               </div>
               <div class="network-circle" data-network="glo">
                  <img src="./assets/images/networks/glo-logo.png" alt="Glo" class="network-logo"
                       onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                  <div style="display: none; font-weight: bold; color: #00A651; font-size: 24px;">GLO</div>
                  <span class="network-name">Glo</span>
               </div>
               <div class="network-circle" data-network="9mobile">
                  <img src="./assets/images/networks/9mobile-logo.png" alt="9mobile" class="network-logo"
                       onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                  <div style="display: none; font-weight: bold; color: #00A86B; font-size: 20px;">9mobile</div>
                  <span class="network-name">9mobile</span>
               </div>
               <div class="network-circle" data-network="airtel">
                  <img src="./assets/images/networks/airtel-logo.png" alt="Airtel" class="network-logo"
                       onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                  <div style="display: none; font-weight: bold; color: #E60012; font-size: 22px;">Airtel</div>
                  <span class="network-name">Airtel</span>
               </div>
            </div>
         </div>
      </section>
      <!--Network Selection Section end-->

      <!--Dynamic Pricing Tables Section-->
      <section class="py-xl-7 py-5 bg-light-subtle">
         <div class="container">
            <div class="row">
               <div class="col-lg-10 offset-lg-1 col-12">
                  <div class="text-center mb-6">
                     <h2 class="mb-3" id="network-title">MTN Data Plans</h2>
                     <p class="mb-0">All prices shown are the network's official rates. Add only ₦10 when purchasing through Aidapay.</p>
                  </div>
               </div>
            </div>

            <!-- MTN Pricing Table -->
            <div class="pricing-table active" id="mtn-table">
               <div class="row">
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">1GB</div>
                              <div class="plan-validity">Daily Plan - 24 Hours</div>
                           </div>
                           <div class="plan-price">₦500</div>
                        </div>
                        <ul class="plan-features">
                           <li>1.5 minutes voice calls included</li>
                           <li>Valid for 24 hours</li>
                           <li>Text "155" to 312</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">2GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦1,500</div>
                        </div>
                        <ul class="plan-features">
                           <li>2 minutes voice calls included</li>
                           <li>200MB YouTube All Day</li>
                           <li>2GB All Night Streaming</li>
                           <li>Valid for 30 days</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">10GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦4,500</div>
                        </div>
                        <ul class="plan-features">
                           <li>10 minutes voice calls included</li>
                           <li>200MB YouTube All Day</li>
                           <li>2GB All Night Streaming</li>
                           <li>Eligible for data roaming</li>
                           <li>Valid for 30 days</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">6GB</div>
                              <div class="plan-validity">Weekly Plan - 7 Days</div>
                           </div>
                           <div class="plan-price">₦2,500</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 7 days</li>
                           <li>Text "143" to 312</li>
                           <li>High-speed 4G+ network</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">36GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦11,000</div>
                        </div>
                        <ul class="plan-features">
                           <li>Eligible for data roaming</li>
                           <li>Valid for 30 days</li>
                           <li>Text "117" to 312</li>
                           <li>Premium high-speed access</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">75GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦18,000</div>
                        </div>
                        <ul class="plan-features">
                           <li>Eligible for data roaming</li>
                           <li>Valid for 30 days</li>
                           <li>Text "150" to 312</li>
                           <li>Best value for heavy users</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
               </div>
            </div>

            <!-- Glo Pricing Table -->
            <div class="pricing-table" id="glo-table">
               <div class="row">
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">1GB</div>
                              <div class="plan-validity">Daily Plan - 24 Hours</div>
                           </div>
                           <div class="plan-price">₦300</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 24 hours</li>
                           <li>4G LTE network access</li>
                           <li>Dial *777# to subscribe</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">2.5GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦1,000</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 30 days</li>
                           <li>Glo 4G+ network</li>
                           <li>Always-on data plan</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">7.5GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦2,500</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 30 days</li>
                           <li>High-speed 4G access</li>
                           <li>Great value for money</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">15GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦5,000</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 30 days</li>
                           <li>Premium 4G+ speeds</li>
                           <li>Perfect for streaming</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">30GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦10,000</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 30 days</li>
                           <li>Unlimited night browsing</li>
                           <li>Best for heavy users</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">60GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦18,000</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 30 days</li>
                           <li>Premium unlimited access</li>
                           <li>Business-grade speeds</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
               </div>
            </div>

            <!-- 9mobile Pricing Table -->
            <div class="pricing-table" id="9mobile-table">
               <div class="row">
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">1GB</div>
                              <div class="plan-validity">Daily Plan - 24 Hours</div>
                           </div>
                           <div class="plan-price">₦300</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 24 hours</li>
                           <li>4G LTE network access</li>
                           <li>Dial *229*3*8# to subscribe</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">1.5GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦1,000</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 30 days</li>
                           <li>9mobile 4G network</li>
                           <li>Smartpaks bundle</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">4.5GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦2,500</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 30 days</li>
                           <li>High-speed 4G access</li>
                           <li>Data sharing enabled</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">11GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦5,000</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 30 days</li>
                           <li>Premium 4G+ speeds</li>
                           <li>Multi-device support</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">25GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦10,000</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 30 days</li>
                           <li>Family sharing enabled</li>
                           <li>Best for heavy usage</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">50GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦18,000</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 30 days</li>
                           <li>Unlimited night browsing</li>
                           <li>Business-grade package</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
               </div>
            </div>

            <!-- Airtel Pricing Table -->
            <div class="pricing-table" id="airtel-table">
               <div class="row">
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">1GB</div>
                              <div class="plan-validity">Daily Plan - 24 Hours</div>
                           </div>
                           <div class="plan-price">₦350</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 24 hours</li>
                           <li>4G LTE network access</li>
                           <li>Dial *141*712*11# to subscribe</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">2GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦1,200</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 30 days</li>
                           <li>Airtel 4G+ network</li>
                           <li>SmartConnect bundle</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">6GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦2,500</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 30 days</li>
                           <li>High-speed 4G access</li>
                           <li>Data rollover enabled</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">12GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦5,000</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 30 days</li>
                           <li>Premium 4G+ speeds</li>
                           <li>Unlimited social media</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">30GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦10,000</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 30 days</li>
                           <li>Unlimited night browsing</li>
                           <li>Best for power users</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
                  <div class="col-lg-4 col-md-6 mb-4">
                     <div class="plan-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                           <div>
                              <div class="plan-data">75GB</div>
                              <div class="plan-validity">Monthly Plan - 30 Days</div>
                           </div>
                           <div class="plan-price">₦20,000</div>
                        </div>
                        <ul class="plan-features">
                           <li>Valid for 30 days</li>
                           <li>Premium unlimited access</li>
                           <li>Enterprise-grade speeds</li>
                        </ul>
                        <button class="btn btn-primary w-100">Buy via Aidapay (+₦10)</button>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </section>
      <!--Dynamic Pricing Tables Section end-->

      <!--CTA Section-->
      <section class="py-xl-9 py-6">
         <div class="container">
            <div class="row">
               <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                  <div class="text-center">
                     <h2 class="mb-3">Ready to Buy Data?</h2>
                     <p class="mb-4 lead">Download the Aidapay app and start purchasing data with just a ₦10 service fee.
                        Fast, reliable, and transparent pricing.</p>
                     <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                        <a href="#" class="btn btn-primary btn-lg">
                           <i class="bi bi-apple me-2"></i>Download for iOS
                        </a>
                        <a href="#" class="btn btn-outline-primary btn-lg">
                           <i class="bi bi-google-play me-2"></i>Download for Android
                        </a>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </section>
      <!--CTA Section end-->
   </main>

   <!--Footer-->
   <footer class="footer bg-dark">
      <div class="container">
         <div class="row align-items-center">
            <div class="col-md-6 col-12">
               <div class="d-flex align-items-center">
                  <img src="./assets/images/logo/logo_aidapay.svg" alt="Aidapay" style="width: 120px;" />
               </div>
            </div>
            <div class="col-md-6 col-12">
               <div class="text-md-end text-center">
                  <small class="mb-0 text-body-secondary">
                     © <span id="copyright">
                        <script>document.getElementById('copyright').appendChild(document.createTextNode(new Date().getFullYear()))</script>
                     </span>
                     Aidapay. All rights reserved.
                  </small>
               </div>
            </div>
         </div>
      </div>
   </footer>
   <!--Footer end-->

   <!-- Scroll top -->
   <div class="btn-scroll-top">
      <svg class="progress-square svg-content" width="100%" height="100%" viewBox="0 0 40 40">
         <path d="M8,1 L32,1 L32,32 L8,32 Z" style="fill: none; stroke: rgb(139, 61, 255); stroke-width: 1; stroke-dasharray: 126, 126; stroke-dashoffset: 126;"></path>
      </svg>
   </div>

   <!-- Libs JS -->
   <script src="./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
   <script src="./assets/libs/simplebar/dist/simplebar.min.js"></script>
   <script src="./assets/libs/headhesive/dist/headhesive.min.js"></script>

   <!-- Scroll Cue -->
   <script src="./assets/libs/scrollcue/scrollCue.min.js"></script>

   <!-- Swiper JS -->
   <script src="./assets/libs/swiper/swiper-bundle.min.js"></script>

   <!-- Theme JS -->
   <script src="./assets/js/theme.min.js"></script>

   <!-- Custom JavaScript for Network Selection -->
   <script>
      document.addEventListener('DOMContentLoaded', function() {
         const networkCircles = document.querySelectorAll('.network-circle');
         const pricingTables = document.querySelectorAll('.pricing-table');
         const networkTitle = document.getElementById('network-title');

         const networkNames = {
            'mtn': 'MTN Data Plans',
            'glo': 'Glo Data Plans',
            '9mobile': '9mobile Data Plans',
            'airtel': 'Airtel Data Plans'
         };

         function switchNetwork(selectedNetwork) {
            // Remove active class from all circles
            networkCircles.forEach(circle => {
               circle.classList.remove('active');
            });

            // Hide all pricing tables
            pricingTables.forEach(table => {
               table.classList.remove('active');
            });

            // Add active class to selected circle
            const selectedCircle = document.querySelector(`[data-network="${selectedNetwork}"]`);
            if (selectedCircle) {
               selectedCircle.classList.add('active');
            }

            // Show selected pricing table
            const selectedTable = document.getElementById(`${selectedNetwork}-table`);
            if (selectedTable) {
               selectedTable.classList.add('active');
            }

            // Update title
            if (networkNames[selectedNetwork]) {
               networkTitle.textContent = networkNames[selectedNetwork];
            }
         }

         // Add click event listeners to network circles
         networkCircles.forEach(circle => {
            circle.addEventListener('click', function() {
               const network = this.getAttribute('data-network');
               switchNetwork(network);
            });
         });

         // Initialize with MTN selected by default
         switchNetwork('mtn');
      });
   </script>

</body>
</html>
